<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.gclife</groupId>
        <artifactId>gclife-business-core</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gclife</groupId>
    <artifactId>gclife-system-base</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>系统管理服务</description>

    <properties>
        <spring.data.datasource.url>*****************************************</spring.data.datasource.url>
        <spring.data.datasource.driver.classname>org.postgresql.Driver</spring.data.datasource.driver.classname>
        <spring.data.datasource.username>policy</spring.data.datasource.username>
        <spring.data.datasource.password>${env.PGPW_FOR_BUILD}</spring.data.datasource.password>
        <spring.context.path>/system-manage</spring.context.path>
        <server.port>21900</server.port>
    </properties>

    <developers>
        <developer>
            <name>caoqinghua</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <dependencies>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-system-core</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

    </dependencies>


</project>
