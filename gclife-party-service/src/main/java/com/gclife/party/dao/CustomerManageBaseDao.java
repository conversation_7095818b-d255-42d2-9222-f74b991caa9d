package com.gclife.party.dao;

import com.gclife.common.model.base.Users;
import com.gclife.party.core.jooq.tables.pojos.CustomerAgentPo;
import com.gclife.party.core.jooq.tables.pojos.CustomerRelationshipPo;
import com.gclife.party.model.bo.CustomerMessageBo;
import com.gclife.party.model.bo.CustomerMessagesBo;
import com.gclife.party.model.bo.CustomerRelationshipBo;
import com.gclife.party.model.bo.MedalBo;
import com.gclife.party.model.request.CustomerMessagesRequest;
import com.gclife.party.model.request.customer.CustomerListRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create 17-11-11
 * description:
 */
public interface CustomerManageBaseDao {
    /**
     * 查询客户列表
     * @param customerListRequest
     * @param userId
     * @return
     */
    List<CustomerMessagesBo> listCustomer(CustomerListRequest customerListRequest, String userId);

    /***
     * 根据用户获取  客户信息
     * @param users
     * @return
     */
    List<CustomerMessagesBo> getCustomerMessages(Users users, CustomerMessagesRequest customerMessagesRequest);

    /**
     * 查询用户的  客户的 信息
     *
     * @param medalNo
     * @return
     */
    CustomerMessageBo getCustomerMessage(String medalNo, String userId, String customerId);

    /**
     * 查询客户是否有勋章
     * @param customerId
     * @return
     */
    List<MedalBo> getMedalList(String customerId);

    /**
     * 根据编码 获取 勋章信息
     * @param medalNo
     * @return
     */
    MedalBo getMedalIdByOn(String medalNo);

    /**
     * 根据  身份证号  姓名获取 用户信息
     * @param idNo
     * @return
     */
    List<CustomerAgentPo> findByIdNo(String idType, String idNo, String userId);

    /**
     * 根据  身份证号  姓名获取 用户信息
     * @param idNo
     * @return
     */
    List<CustomerAgentPo> findByIdOrFourElements(String customerId, String name, String idNo, Long birthday, String sex, String userId);

    /**
     * 获取客户之间关系
     * @param customerId 客户ID
     * @param level 查询深度
     * @return
     */
    List<CustomerRelationshipBo> listMember(String customerId, Integer level);

    /**
     * 批量获取客户之间关系
     * @param customerIds 客户ID
     * @param level 查询深度
     * @return
     */
    Map<String, List<CustomerRelationshipBo>> listMember(List<String> customerIds, Integer level);

    /**
     * 根据客户ID和关系客户ID查找关系数据
     * @param customerId 客户ID
     * @param relationCustomerId 关系客户ID
     * @return
     */
    List<CustomerRelationshipPo> listCustomerRelationship(String customerId, String relationCustomerId);

    /**
     * 生日客户列表
     * @param userId 用户ID
     * @return
     */
    List<CustomerMessagesBo> listBirthdayCustomer(String userId);

    /**
     * 查询非家庭成员客户列表
     * @param customerListRequest
     * @param userId 用户ID
     * @return
     */
    List<CustomerAgentPo> listChooseCustomer(CustomerListRequest customerListRequest, String userId);
}