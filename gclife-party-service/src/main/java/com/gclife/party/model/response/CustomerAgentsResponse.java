package com.gclife.party.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * create 17-11-14
 * description:
 */
@Data
public class CustomerAgentsResponse {

    @ApiModelProperty(example = "代理人客户主键ID")
    private String customerAgentId;
    @ApiModelProperty(example = "客户主键ID")
    private String customerId;
    @ApiModelProperty(example = "客户版本")
    private String versionNo;
    @ApiModelProperty(example = "客户号")
    private String customerNo;
    @ApiModelProperty(example = "姓名")
    private String name;
    @ApiModelProperty(example = "昵称")
    private String nickName;
    @ApiModelProperty(example = "证件类型")
    private String idType;
    @ApiModelProperty(example = "证件类型名称")
    private String idTypeName;
    @ApiModelProperty(example = "性别")
    private String sex;
    @ApiModelProperty(example = "性别名称")
    private String sexName;
    @ApiModelProperty(example = "出生日期")
    private Long birthday;
    @ApiModelProperty(example = "证件号码")
    private String idNo;
    @ApiModelProperty(example = "证件有效期")
    private Long idExpDate;
    @ApiModelProperty(example = "通讯地址")
    private String postalAddress;
    @ApiModelProperty(example = "通讯邮编")
    private String zipCode;
    @ApiModelProperty(example = "通讯电话")
    private String phone;
    @ApiModelProperty(example = "通讯传真")
    private String fax;
    @ApiModelProperty(example = "家庭地址")
    private String homeAddress;
    @ApiModelProperty(example = "家庭邮编")
    private String homeZipCode;
    @ApiModelProperty(example = "家庭电话")
    private String homePhone;
    @ApiModelProperty(example = "家庭传真")
    private String homeFax;
    @ApiModelProperty(example = "国籍")
    private String nationality;
    @ApiModelProperty(example = "民族")
    private String nations;
    @ApiModelProperty(example = "户口所在地")
    private String registerAddress;
    @ApiModelProperty(example = "婚姻状况")
    private String marriage;
    @ApiModelProperty(example = "健康状况")
    private String health;
    @ApiModelProperty(example = "身高")
    private String stature;
    @ApiModelProperty(example = "体重")
    private String avoirdupois;
    @ApiModelProperty(example = "学历")
    private String degree;
    @ApiModelProperty(example = "信用等级")
    private String creditGrade;
    @ApiModelProperty(example = "是否吸烟标志")
    private String smokeFlag;
    @ApiModelProperty(example = "身体指标")
    private String bmi;
    @ApiModelProperty(example = "驾照")
    private String license;
    @ApiModelProperty(example = "驾照类型")
    private String licenseType;
    @ApiModelProperty(example = "职业类别")
    private String occupationType;
    @ApiModelProperty(example = "职业代码")
    private String occupationCode;
    @ApiModelProperty(example = "职业（工种）")
    private String workType;
    @ApiModelProperty(example = "兼职（工种）")
    private String pluralityType;
    @ApiModelProperty(example = "工资")
    private String salary;
    @ApiModelProperty(example = "是否有社保标志")
    private String socialSecurity;
    @ApiModelProperty(example = "单位地址")
    private String belongsCompanyAddress;
    @ApiModelProperty(example = "单位邮编")
    private String belongsCompanyZipCode;
    @ApiModelProperty(example = "单位电话")
    private String belongsCompanyPhone;
    @ApiModelProperty(example = "单位传真")
    private String belongsCompanyFax;
    @ApiModelProperty(example = "入司日期")
    private Long joinCompanyDate;
    @ApiModelProperty(example = "参加工作日期")
    private Long startWorkDate;
    @ApiModelProperty(example = "职位")
    private String position;
    @ApiModelProperty(example = "年收入")
    private String income;
    @ApiModelProperty(example = "年收入来源")
    private String incomeSource;
    @ApiModelProperty(example = "家庭收入")
    private String familyIncome;
    @ApiModelProperty(example = "家庭收入来源")
    private String familyIncomeSource;
    @ApiModelProperty(example = "银行编码")
    private String bankCode;
    @ApiModelProperty(example = "银行帐号")
    private String bankAccountNo;
    @ApiModelProperty(example = "银行帐户名")
    private String bankAccountName;
    @ApiModelProperty(example = "单位名词")
    private String companyName;
    @ApiModelProperty(example = "单位证件类型")
    private String companyIdType;
    @ApiModelProperty(example = "单位证件号码")
    private String companyIdNo;
    @ApiModelProperty(example = "单位地址")
    private String companyAddress;
    @ApiModelProperty(example = "单位邮编")
    private String companyZipCode;
    @ApiModelProperty(example = "单位电话")
    private String companyPhone;
    @ApiModelProperty(example = "单位传真")
    private String companyFax;
    @ApiModelProperty(example = "单位联系人")
    private String companyContractName;
    @ApiModelProperty(example = "单位联系人手机")
    private String companyContractMobile;
    @ApiModelProperty(example = "单位联系人地址")
    private String companyContractAddress;
    @ApiModelProperty(example = "其它电话号码")
    private String otherPhone;
    @ApiModelProperty(example = "客户主键ID")
    private String email;
    @ApiModelProperty(example = "邮件地址")
    private String mobile;
    @ApiModelProperty(example = "单位联系人电话")
    private String companyContractPhone;
    @ApiModelProperty(example = "用户ID")
    private String userId;
    @ApiModelProperty(example = "头像")
    private String avatar;
    @ApiModelProperty(example = "家庭地址地区编码")
    private String homeAreaCode;
    @ApiModelProperty(example = "家庭地址地区名称")
    private String homeAreaCodeName;
    @ApiModelProperty(example = "单位地址地区编码")
    private String belongsCompanyAreaCode;
    @ApiModelProperty(example = "单位地址地区编码")
    private String companyAreaCode;
    @ApiModelProperty(example = "分组编号")
    private String groupCode;
    @ApiModelProperty(example = "公司类型")
    private String companyType;
    @ApiModelProperty(value = "微信号",example = "微信号")
    private String wechatNo;

    @ApiModelProperty(example = "脸书号",required = true)
    private String facebookNo;
}